"""
Voice Receptionist Agent using LiveKit Agents and Gemini Live API
"""
import asyncio
import logging

from livekit.agents import (
    AutoSubscribe,
    JobContext,
    WorkerOptions,
    cli,
)
from livekit.agents.voice import Agent as VoiceAgent
from livekit.plugins import google
from livekit import rtc

from config import Config

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def entrypoint(ctx: JobContext):
    """Main entrypoint for the agent"""
    logger.info(f"Starting Voice Receptionist Agent in room: {ctx.room.name}")

    # Validate configuration
    Config.validate()

    # Initialize the Gemini Live API model
    model = google.beta.realtime.RealtimeModel(
        model=Config.GEMINI_MODEL,
        voice=Config.GEMINI_VOICE,
        temperature=Config.GEMINI_TEMPERATURE,
        instructions=Config.SYSTEM_INSTRUCTIONS,
        api_key=Config.GOOGLE_API_KEY,
    )

    logger.info(f"Initialized Voice Receptionist with model: {Config.GEMINI_MODEL}")
    logger.info(f"Using voice: {Config.GEMINI_VOICE}")

    # Wait for the first participant to connect
    await ctx.wait_for_participant()
    logger.info("Participant connected, starting voice agent")

    # Create and start the voice agent
    agent = VoiceAgent(
        llm=model,
        # Note: For Gemini Live API, we don't need separate STT/TTS
        # as the model handles audio input/output natively
    )

    # Start the agent
    agent.start(ctx.room)

    # Initial greeting
    await agent.say(
        "Hello! Welcome to our service. I'm your voice assistant. How can I help you today?",
        allow_interruptions=True
    )

    logger.info("Voice Receptionist Agent is now active and ready to assist")


if __name__ == "__main__":
    # Start the CLI with our worker
    cli.run_app(
        WorkerOptions(
            entrypoint_fnc=entrypoint,
        )
    )
